@custom-variant dark (&:is(.dark *));

:root {
  --radius: .5rem;

  --background-100-value: 0, 0%, 100%;
  --background-200-value: 0, 0%, 97%;

  --ring: var(--color-blue-900-value);
  --primary: var(--color-gray-1000-value);

  --color-gray-alpha-100-value: hsla(0, 0%, 0%, .05);
  --color-gray-alpha-200-value: hsla(0, 0%, 0%, .08);
  --color-gray-alpha-300-value: hsla(0, 0%, 0%, .12);
  --color-gray-alpha-400-value: hsla(0, 0%, 0%, .17);
  --color-gray-alpha-500-value: hsla(0, 0%, 0%, .21);
  --color-gray-alpha-600-value: hsla(0, 0%, 0%, .34);
  --color-gray-alpha-700-value: hsla(0, 0%, 0%, .44);
  --color-gray-alpha-800-value: hsla(0, 0%, 0%, .51);
  --color-gray-alpha-900-value: hsla(0, 0%, 0%, .61);
  --color-gray-alpha-1000-value: hsla(0, 0%, 0%, .91);

  --color-gray-100-value: 0, 0%, 95%;
  --color-gray-200-value: 0, 0%, 93%;
  --color-gray-300-value: 0, 0%, 90%;
  --color-gray-400-value: 0, 0%, 85%;
  --color-gray-500-value: 0, 0%, 79%;
  --color-gray-600-value: 0, 0%, 66%;
  --color-gray-700-value: 0, 0%, 56%;
  --color-gray-800-value: 0, 0%, 49%;
  --color-gray-900-value: 0, 0%, 40%;
  --color-gray-1000-value: 0, 0%, 9%;

  --color-blue-100-value: 212, 100%, 97%;
  --color-blue-200-value: 210, 100%, 96%;
  --color-blue-300-value: 210, 100%, 94%;
  --color-blue-400-value: 209, 100%, 90%;
  --color-blue-500-value: 209, 100%, 80%;
  --color-blue-600-value: 208, 100%, 66%;
  --color-blue-700-value: 212, 100%, 48%;
  --color-blue-800-value: 212, 100%, 41%;
  --color-blue-900-value: 211, 100%, 42%;
  --color-blue-1000-value: 211, 100%, 15%;

  --color-red-100-value: 0, 100%, 97%;
  --color-red-200-value: 0, 100%, 96%;
  --color-red-300-value: 0, 100%, 95%;
  --color-red-400-value: 0, 90%, 92%;
  --color-red-500-value: 0, 82%, 85%;
  --color-red-600-value: 359, 90%, 71%;
  --color-red-700-value: 358, 75%, 59%;
  --color-red-800-value: 358, 70%, 52%;
  --color-red-900-value: 358, 66%, 48%;
  --color-red-1000-value: 355, 49%, 15%;

  --color-amber-100-value: 39, 100%, 95%;
  --color-amber-200-value: 44, 100%, 92%;
  --color-amber-300-value: 43, 96%, 90%;
  --color-amber-400-value: 42, 100%, 78%;
  --color-amber-500-value: 38, 100%, 71%;
  --color-amber-600-value: 36, 90%, 62%;
  --color-amber-700-value: 39, 100%, 57%;
  --color-amber-800-value: 35, 100%, 52%;
  --color-amber-900-value: 30, 100%, 32%;
  --color-amber-1000-value: 20, 79%, 17%;

  --color-green-100-value: 120, 60%, 96%;
  --color-green-200-value: 120, 60%, 95%;
  --color-green-300-value: 120, 60%, 91%;
  --color-green-400-value: 122, 60%, 86%;
  --color-green-500-value: 124, 60%, 75%;
  --color-green-600-value: 125, 60%, 64%;
  --color-green-700-value: 131, 41%, 46%;
  --color-green-800-value: 132, 43%, 39%;
  --color-green-900-value: 133, 50%, 32%;
  --color-green-1000-value: 128, 29%, 15%;

  --color-teal-100-value: 169, 70%, 96%;
  --color-teal-200-value: 167, 70%, 94%;
  --color-teal-300-value: 168, 70%, 90%;
  --color-teal-400-value: 170, 70%, 85%;
  --color-teal-500-value: 170, 70%, 72%;
  --color-teal-600-value: 170, 70%, 57%;
  --color-teal-700-value: 173, 80%, 36%;
  --color-teal-800-value: 173, 83%, 30%;
  --color-teal-900-value: 174, 91%, 25%;
  --color-teal-1000-value: 171, 80%, 13%;

  --color-purple-100-value: 276, 100%, 97%;
  --color-purple-200-value: 277, 87%, 97%;
  --color-purple-300-value: 274, 78%, 95%;
  --color-purple-400-value: 276, 71%, 92%;
  --color-purple-500-value: 274, 70%, 82%;
  --color-purple-600-value: 273, 72%, 73%;
  --color-purple-700-value: 272, 51%, 54%;
  --color-purple-800-value: 272, 47%, 45%;
  --color-purple-900-value: 274, 71%, 43%;
  --color-purple-1000-value: 276, 100%, 15%;

  --color-pink-100-value: 330, 100%, 96%;
  --color-pink-200-value: 340, 90%, 96%;
  --color-pink-300-value: 340, 82%, 94%;
  --color-pink-400-value: 341, 76%, 91%;
  --color-pink-500-value: 340, 75%, 84%;
  --color-pink-600-value: 341, 75%, 73%;
  --color-pink-700-value: 336, 80%, 58%;
  --color-pink-800-value: 336, 74%, 51%;
  --color-pink-900-value: 336, 65%, 45%;
  --color-pink-1000-value: 333, 74%, 15%;

  --shadow-border-default-value: 0 0 0 1px rgba(0,0,0,.08);
  --shadow-border-small-value: 0 0 0 1px rgba(0,0,0,.08), 0 1px 2px 0 rgba(0,0,0,.04);
  --shadow-border-medium-value: 0 0 0 1px rgba(0,0,0,.08), 0px 2px 2px rgba(0,0,0,.04),0px 8px 8px -8px rgba(0,0,0,.04);
  --shadow-border-large-value: 0 0 0 1px rgba(0,0,0,.08), 0px 2px 2px rgba(0,0,0,.04),0px 8px 16px -4px rgba(0,0,0,.04);
  --shadow-border-tooltip-value: 0 0 0 1px rgba(0,0,0,.08),0px 1px 1px rgba(0,0,0,.02),0px 4px 8px rgba(0,0,0,.04);
  --shadow-border-menu-value: 0 0 0 1px rgba(0,0,0,.08),0px 1px 1px rgba(0,0,0,.02),0px 4px 8px -4px rgba(0,0,0,.04),0px 16px 24px -8px rgba(0,0,0,.06);
  --shadow-border-modal-value: 0 0 0 1px rgba(0,0,0,.08),0px 1px 1px rgba(0,0,0,.02),0px 8px 16px -4px rgba(0,0,0,.04),0px 24px 32px -8px rgba(0,0,0,.06);
  --shadow-border-fullscreen-value: 0 0 0 1px rgba(0,0,0,.08),0px 1px 1px rgba(0,0,0,.02),0px 8px 16px -4px rgba(0,0,0,.04),0px 24px 32px -8px rgba(0,0,0,.06);

  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -moz-text-size-adjust: none;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
  text-autospace: auto;
  font-synthesis: none;
  color-scheme: light dark;
  background-color: var(--color-background-100);
  color: var(--color-foreground);
}

:root.dark {
  --background-100-value: 0, 0%, 5%;
  --background-200-value: 0, 0%, 0%;

  --color-gray-alpha-100-value: hsla(0, 0%, 100%, .06);
  --color-gray-alpha-200-value: hsla(0, 0%, 100%, .09);
  --color-gray-alpha-300-value: hsla(0, 0%, 100%, .13);
  --color-gray-alpha-400-value: hsla(0, 0%, 100%, .14);
  --color-gray-alpha-500-value: hsla(0, 0%, 100%, .24);
  --color-gray-alpha-600-value: hsla(0, 0%, 100%, .51);
  --color-gray-alpha-700-value: hsla(0, 0%, 100%, .54);
  --color-gray-alpha-800-value: hsla(0, 0%, 100%, .47);
  --color-gray-alpha-900-value: hsla(0, 0%, 100%, .61);
  --color-gray-alpha-1000-value: hsla(0, 0%, 100%, .92);

  --color-gray-100-value: 0, 0%, 10%;
  --color-gray-200-value: 0, 0%, 12%;
  --color-gray-300-value: 0, 0%, 16%;
  --color-gray-400-value: 0, 0%, 18%;
  --color-gray-500-value: 0, 0%, 27%;
  --color-gray-600-value: 0, 0%, 53%;
  --color-gray-700-value: 0, 0%, 56%;
  --color-gray-800-value: 0, 0%, 49%;
  --color-gray-900-value: 0, 0%, 63%;
  --color-gray-1000-value: 0, 0%, 93%;

  --color-blue-100-value: 216, 50%, 12%;
  --color-blue-200-value: 214, 59%, 15%;
  --color-blue-300-value: 213, 71%, 20%;
  --color-blue-400-value: 212, 78%, 23%;
  --color-blue-500-value: 211, 86%, 27%;
  --color-blue-600-value: 206, 100%, 50%;
  --color-blue-700-value: 212, 100%, 48%;
  --color-blue-800-value: 212, 100%, 41%;
  --color-blue-900-value: 210, 100%, 66%;
  --color-blue-1000-value: 206, 100%, 96%;

  --color-red-100-value: 357, 37%, 12%;
  --color-red-200-value: 357, 46%, 16%;
  --color-red-300-value: 356, 54%, 22%;
  --color-red-400-value: 357, 55%, 26%;
  --color-red-500-value: 357, 60%, 32%;
  --color-red-600-value: 358, 75%, 59%;
  --color-red-700-value: 358, 75%, 59%;
  --color-red-800-value: 358, 69%, 52%;
  --color-red-900-value: 358, 100%, 69%;
  --color-red-1000-value: 353, 90%, 96%;

  --color-amber-100-value: 35, 100%, 8%;
  --color-amber-200-value: 32, 100%, 10%;
  --color-amber-300-value: 33, 100%, 15%;
  --color-amber-400-value: 35, 100%, 17%;
  --color-amber-500-value: 35, 91%, 22%;
  --color-amber-600-value: 39, 85%, 49%;
  --color-amber-700-value: 39, 100%, 57%;
  --color-amber-800-value: 35, 100%, 52%;
  --color-amber-900-value: 39, 90%, 50%;
  --color-amber-1000-value: 40, 94%, 93%;

  --color-green-100-value: 136, 50%, 9%;
  --color-green-200-value: 137, 50%, 12%;
  --color-green-300-value: 136, 50%, 14%;
  --color-green-400-value: 135, 70%, 16%;
  --color-green-500-value: 135, 70%, 23%;
  --color-green-600-value: 135, 70%, 34%;
  --color-green-700-value: 131, 41%, 46%;
  --color-green-800-value: 132, 43%, 39%;
  --color-green-900-value: 131, 43%, 57%;
  --color-green-1000-value: 136, 73%, 94%;

  --color-teal-100-value: 169, 78%, 7%;
  --color-teal-200-value: 170, 74%, 9%;
  --color-teal-300-value: 171, 75%, 13%;
  --color-teal-400-value: 171, 85%, 13%;
  --color-teal-500-value: 172, 85%, 20%;
  --color-teal-600-value: 172, 85%, 32%;
  --color-teal-700-value: 173, 80%, 36%;
  --color-teal-800-value: 173, 83%, 30%;
  --color-teal-900-value: 174, 90%, 41%;
  --color-teal-1000-value: 166, 71%, 93%;

  --color-purple-100-value: 283, 30%, 12%;
  --color-purple-200-value: 281, 38%, 16%;
  --color-purple-300-value: 279, 44%, 23%;
  --color-purple-400-value: 277, 46%, 28%;
  --color-purple-500-value: 274, 49%, 35%;
  --color-purple-600-value: 272, 51%, 54%;
  --color-purple-700-value: 272, 51%, 54%;
  --color-purple-800-value: 272, 47%, 45%;
  --color-purple-900-value: 275, 80%, 71%;
  --color-purple-1000-value: 281, 73%, 96%;

  --color-pink-100-value: 335, 32%, 12%;
  --color-pink-200-value: 335, 43%, 16%;
  --color-pink-300-value: 335, 47%, 21%;
  --color-pink-400-value: 335, 51%, 22%;
  --color-pink-500-value: 335, 57%, 27%;
  --color-pink-600-value: 336, 75%, 40%;
  --color-pink-700-value: 336, 80%, 58%;
  --color-pink-800-value: 336, 74%, 51%;
  --color-pink-900-value: 341, 90%, 67%;
  --color-pink-1000-value: 333, 90%, 96%;

  --shadow-border-default-value: 0 0 0 1px rgba(255,255,255,.145);
  --shadow-border-small-value: 0 0 0 1px rgba(255,255,255,.145),0 1px 2px 0 rgba(0,0,0,.04);
  --shadow-border-medium-value: 0 0 0 1px rgba(255,255,255,.145),0px 2px 2px rgba(0,0,0,.32),0px 8px 8px -8px rgba(0,0,0,.16);
  --shadow-border-large-value: 0 0 0 1px rgba(255,255,255,.145),0px 2px 2px rgba(0,0,0,.04),0px 8px 16px -4px rgba(0,0,0,.04);
  --shadow-border-tooltip-value: 0 0 0 1px rgba(255,255,255,.145),0px 1px 1px rgba(0,0,0,.02),0px 4px 8px rgba(0,0,0,.04);
  --shadow-border-menu-value: 0 0 0 1px rgba(255,255,255,.145),0px 1px 1px rgba(0,0,0,.02),0px 4px 8px -4px rgba(0,0,0,.04),0px 16px 24px -8px rgba(0,0,0,.06);
  --shadow-border-modal-value: 0 0 0 1px rgba(255,255,255,.145),0px 1px 1px rgba(0,0,0,.02),0px 8px 16px -4px rgba(0,0,0,.04),0px 24px 32px -8px rgba(0,0,0,.06);
  --shadow-border-fullscreen-value: 0 0 0 1px rgba(255,255,255,.145),0px 1px 1px rgba(0,0,0,.02),0px 8px 16px -4px rgba(0,0,0,.04),0px 24px 32px -8px rgba(0,0,0,.06);

  color-scheme: dark;
}

@theme inline {
  --color-primary: hsl(var(--primary));

  --color-input: var(--color-gray-alpha-300);
  --color-border: hsl(var(--color-gray-300-value));
  --color-border-hover: hsl(var(--color-gray-500-value));
  --color-border-active: hsl(var(--color-gray-600-value));

  --color-foreground: hsl(var(--color-gray-1000-value));
  --color-foreground-secondary: hsl(var(--color-gray-900-value));
  --color-background-100: hsl(var(--background-100-value));
  --color-background-200: hsl(var(--background-200-value));
  --color-background-hover: hsl(var(--color-gray-200-value));
  --color-background-active: hsl(var(--color-gray-300-value));

  --color-gray-alpha-100: var(--color-gray-alpha-100-value);
  --color-gray-alpha-200: var(--color-gray-alpha-200-value);
  --color-gray-alpha-300: var(--color-gray-alpha-300-value);
  --color-gray-alpha-400: var(--color-gray-alpha-400-value);
  --color-gray-alpha-500: var(--color-gray-alpha-500-value);
  --color-gray-alpha-600: var(--color-gray-alpha-600-value);
  --color-gray-alpha-700: var(--color-gray-alpha-700-value);
  --color-gray-alpha-800: var(--color-gray-alpha-800-value);
  --color-gray-alpha-900: var(--color-gray-alpha-900-value);
  --color-gray-alpha-1000: var(--color-gray-alpha-1000-value);

  --color-gray-100: hsl(var(--color-gray-100-value));
  --color-gray-200: hsl(var(--color-gray-200-value));
  --color-gray-300: hsl(var(--color-gray-300-value));
  --color-gray-400: hsl(var(--color-gray-400-value));
  --color-gray-500: hsl(var(--color-gray-500-value));
  --color-gray-600: hsl(var(--color-gray-600-value));
  --color-gray-700: hsl(var(--color-gray-700-value));
  --color-gray-800: hsl(var(--color-gray-800-value));
  --color-gray-900: hsl(var(--color-gray-900-value));
  --color-gray-1000: hsl(var(--color-gray-1000-value));

  --color-red-100: hsl(var(--color-red-100-value));
  --color-red-200: hsl(var(--color-red-200-value));
  --color-red-300: hsl(var(--color-red-300-value));
  --color-red-400: hsl(var(--color-red-400-value));
  --color-red-500: hsl(var(--color-red-500-value));
  --color-red-600: hsl(var(--color-red-600-value));
  --color-red-700: hsl(var(--color-red-700-value));
  --color-red-800: hsl(var(--color-red-800-value));
  --color-red-900: hsl(var(--color-red-900-value));
  --color-red-1000: hsl(var(--color-red-1000-value));

  --color-blue-100: hsl(var(--color-blue-100-value));
  --color-blue-200: hsl(var(--color-blue-200-value));
  --color-blue-300: hsl(var(--color-blue-300-value));
  --color-blue-400: hsl(var(--color-blue-400-value));
  --color-blue-500: hsl(var(--color-blue-500-value));
  --color-blue-600: hsl(var(--color-blue-600-value));
  --color-blue-700: hsl(var(--color-blue-700-value));
  --color-blue-800: hsl(var(--color-blue-800-value));
  --color-blue-900: hsl(var(--color-blue-900-value));
  --color-blue-1000: hsl(var(--color-blue-1000-value));

  --color-amber-100: hsl(var(--color-amber-100-value));
  --color-amber-200: hsl(var(--color-amber-200-value));
  --color-amber-300: hsl(var(--color-amber-300-value));
  --color-amber-400: hsl(var(--color-amber-400-value));
  --color-amber-500: hsl(var(--color-amber-500-value));
  --color-amber-600: hsl(var(--color-amber-600-value));
  --color-amber-700: hsl(var(--color-amber-700-value));
  --color-amber-800: hsl(var(--color-amber-800-value));
  --color-amber-900: hsl(var(--color-amber-900-value));
  --color-amber-1000: hsl(var(--color-amber-1000-value));

  --color-green-100: hsl(var(--color-green-100-value));
  --color-green-200: hsl(var(--color-green-200-value));
  --color-green-300: hsl(var(--color-green-300-value));
  --color-green-400: hsl(var(--color-green-400-value));
  --color-green-500: hsl(var(--color-green-500-value));
  --color-green-600: hsl(var(--color-green-600-value));
  --color-green-700: hsl(var(--color-green-700-value));
  --color-green-800: hsl(var(--color-green-800-value));
  --color-green-900: hsl(var(--color-green-900-value));
  --color-green-1000: hsl(var(--color-green-1000-value));

  --color-teal-100: hsl(var(--color-teal-100-value));
  --color-teal-200: hsl(var(--color-teal-200-value));
  --color-teal-300: hsl(var(--color-teal-300-value));
  --color-teal-400: hsl(var(--color-teal-400-value));
  --color-teal-500: hsl(var(--color-teal-500-value));
  --color-teal-600: hsl(var(--color-teal-600-value));
  --color-teal-700: hsl(var(--color-teal-700-value));
  --color-teal-800: hsl(var(--color-teal-800-value));
  --color-teal-900: hsl(var(--color-teal-900-value));
  --color-teal-1000: hsl(var(--color-teal-1000-value));

  --color-purple-100: hsl(var(--color-purple-100-value));
  --color-purple-200: hsl(var(--color-purple-200-value));
  --color-purple-300: hsl(var(--color-purple-300-value));
  --color-purple-400: hsl(var(--color-purple-400-value));
  --color-purple-500: hsl(var(--color-purple-500-value));
  --color-purple-600: hsl(var(--color-purple-600-value));
  --color-purple-700: hsl(var(--color-purple-700-value));
  --color-purple-800: hsl(var(--color-purple-800-value));
  --color-purple-900: hsl(var(--color-purple-900-value));
  --color-purple-1000: hsl(var(--color-purple-1000-value));

  --color-pink-100: hsl(var(--color-pink-100-value));
  --color-pink-200: hsl(var(--color-pink-200-value));
  --color-pink-300: hsl(var(--color-pink-300-value));
  --color-pink-400: hsl(var(--color-pink-400-value));
  --color-pink-500: hsl(var(--color-pink-500-value));
  --color-pink-600: hsl(var(--color-pink-600-value));
  --color-pink-700: hsl(var(--color-pink-700-value));
  --color-pink-800: hsl(var(--color-pink-800-value));
  --color-pink-900: hsl(var(--color-pink-900-value));
  --color-pink-1000: hsl(var(--color-pink-1000-value));

  --z-1: 1;
  --spacing-em: 1em;

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --radius-inherit: inherit;

  --shadow-border-default: var(--shadow-border-default-value);
  --shadow-border-small: var(--shadow-border-small-value);
  --shadow-border-medium: var(--shadow-border-medium-value);
  --shadow-border-large: var(--shadow-border-large-value);
  --shadow-border-tooltip: var(--shadow-border-tooltip-value);
  --shadow-border-menu: var(--shadow-border-menu-value);
  --shadow-border-modal: var(--shadow-border-modal-value);
  --shadow-border-fullscreen: var(--shadow-border-fullscreen-value);

  --default-transition-duration: .2s;
  --default-transition-timing-function: ease-in-out;
}

@utility scrollbar-hidden {
  & {
    scrollbar-width: none;
    -ms-overflow-style: none;
    -webkit-overflow-scrolling: touch;
  }

  &::-webkit-scrollbar {
    display: none;
  }
}

@utility peer-focus-ring {
  .peer:focus-visible ~ & {
    box-shadow: 0 0 0 2px var(--color-background-100), 0 0 0 4px hsl(var(--ring));
  }
}

@utility self-focus-ring {
  &:focus-visible {
    box-shadow: 0 0 0 2px var(--color-background-100), 0 0 0 4px hsl(var(--ring));
  }
}

@utility smallest {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

@layer components {
  .pxd-input--border {
    border: 1px solid var(--border-color, var(--color-gray-alpha-300));

    &:not(.is-disabled, .is-readonly):hover,
    &:not(.is-disabled, .is-readonly):focus-within {
      /* --border-color: var(--color-gray-alpha-600); */
      --border-color: var(--color-primary);
    }

    &:not(.is-disabled, .is-readonly):focus-within {
      /* box-shadow: 0 0 0 3px var(--color-gray-alpha-300); */
      box-shadow: 0 0 0 3px hsla(var(--primary), .2);
    }

    &.is-error {
      &:not(:focus)::placeholder {
        color: var(--color-red-900) !important;
      }

      &, &:focus-within {
        --border-color: hsl(var(--color-red-900-value));
        box-shadow: 0 0 0 3px hsl(var(--color-red-300-value));
      }

      &:focus-within,
      &:not(.is-disabled, .is-readonly):hover {
        --border-color: hsl(var(--color-red-900-value));
        box-shadow: 0 0 0 3px hsl(var(--color-red-500-value));
      }
    }
  }

  .pxd-form--label {
    max-width: 100%;
    margin-bottom: calc(var(--spacing) * 1.5);
    font-size: var(--text-sm);
    color: var(--color-foreground-secondary);
  }
}

@layer base {
  * {
    @apply border-border;
  }
}

@media (prefers-reduced-motion: reduce) {
  :root {
    --default-transition-duration: 0 !important;
  }
}

.pxd-transition--fade-enter-active,
.pxd-transition--fade-leave-active {
  transition: opacity var(--default-transition-duration) var(--default-transition-timing-function);
}

.pxd-transition--fade-enter-from,
.pxd-transition--fade-leave-to {
  opacity: 0;
}

.pxd-transition--fade-scale-enter-active,
.pxd-transition--fade-scale-leave-active {
  transition: all var(--default-transition-duration) var(--default-transition-timing-function);
}

.pxd-transition--fade-scale-enter-from,
.pxd-transition--fade-scale-leave-to {
  opacity: 0;
  transform: scale(.8);
}

.line-clamp {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: var(--line-clamp, 1);
  -webkit-box-orient: vertical;
}

.scroll-disabled {
  overflow: clip !important;
  scrollbar-gutter: stable !important;
}
