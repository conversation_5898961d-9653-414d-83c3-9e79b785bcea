{"name": "docs", "type": "module", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"canvas-confetti": "^1.9.3", "pxd": "workspace:*", "vue": "catalog:"}, "devDependencies": {"@shikijs/markdown-it": "^3.8.1", "@tailwindcss/vite": "catalog:", "@unhead/vue": "^2.0.13", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/compiler-sfc": "catalog:", "@vue/tsconfig": "catalog:", "markdown-it-anchor": "^9.2.0", "markdown-it-attrs": "^4.3.1", "markdown-it-plugins": "^0.4.0", "shiki": "^3.3.0", "tailwindcss": "catalog:", "typescript": "catalog:", "unplugin-auto-import": "catalog:", "unplugin-vue-components": "catalog:", "unplugin-vue-router": "catalog:", "vite": "catalog:", "vite-plugin-vue-meta-layouts": "^0.5.1", "vite-vue-md": "^1.4.0", "vue-router": "^4.5.0"}}