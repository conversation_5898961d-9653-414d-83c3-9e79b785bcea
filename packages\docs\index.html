<!DOCTYPE html>
<html lang="en" class="overflow-y-scroll">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <link rel="icon" href="/favicon-light.ico" type="image/x-icon" media="(prefers-color-scheme: light)">
    <link rel="icon" href="/favicon-dark.ico" type="image/x-icon" media="(prefers-color-scheme: dark)">
    <meta property="keywords" content="PXD,Vue,UI,Component,Library">
    <meta property="description" content="PXD is a Vue UI Component Library. Compatible with Vue2&3 without changing versions.">
    <meta property="og:site_name" content="PXD">
    <meta property="og:url" content="https://pxd-zeta.vercel.app">
    <meta property="og:title" content="PXD - A universal UI component library for Vue2&3">
    <meta property="og:description" content="Compatible with Vue2&3 without changing versions." />
    <meta property="twitter:card" content="summary">
    <meta property="twitter:title" content="PXD - A universal UI component library for Vue2&3">
    <meta property="twitter:description" content="Compatible with Vue2&3 without changing versions.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PXD - A universal UI component library for Vue2&3</title>
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
