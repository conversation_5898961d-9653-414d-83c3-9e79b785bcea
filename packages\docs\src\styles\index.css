@import 'tailwindcss';
@import '../../node_modules/pxd/dist/styles/tw.css';
@source '../../node_modules/pxd';
@source '../../node_modules/markdown-it-plugins';

@theme {
  --font-sans: '<PERSON>','Inter Fallback','<PERSON>eist',ui-sans-serif,system-ui,sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji';
  --default-mono-font-family: 'Geist Mono', 'JetBrains Mono', 'MonoLisa', ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

:root {
  font-feature-settings: 'liga' 1, 'calt' 1;
}

html,
body,
#app {
  @apply h-full;
}

::selection {
  background-color: var(--color-green-1000);
  color: var(--color-green-100);
}

:not(pre) > code {
  font-size: 0.875rem;
  margin-right: 0.22em;
  padding: 0.15em 0.5em;
  border-radius: 0.25em;
  white-space: nowrap;
  background-color: var(--color-gray-alpha-200);
  border: 1px solid var(--color-gray-alpha-300);
  color: var(--color-foreground);
}
